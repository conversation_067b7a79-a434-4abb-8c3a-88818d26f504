'use client'

import React, { useState, useRef, useEffect } from 'react';
import { fabric } from 'fabric';
import { CanvasSidebar } from './CanvasSidebar';
import { CanvasToolbar } from './CanvasToolbar';
import { cn } from '@/common/utils/helpers';

interface CanvasEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave?: (imageUrl: string) => void;
  initialImage?: string;
  className?: string;
}

export const CanvasEditor = ({
  isOpen,
  onClose,
  onSave,
  initialImage,
  className
}: CanvasEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [fabricCanvas, setFabricCanvas] = useState<fabric.Canvas | null>(null);
  const [selectedTool, setSelectedTool] = useState<string>('select');
  const [canvasHistory, setCanvasHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Initialize fabric canvas
  useEffect(() => {
    if (!canvasRef.current || !isOpen) return;

    const canvas = new fabric.Canvas(canvasRef.current, {
      width: 800,
      height: 600,
      backgroundColor: '#ffffff',
      selection: true,
      preserveObjectStacking: true,
    });

    // Enable zoom and pan functionality
    canvas.on('mouse:wheel', function(opt) {
      const delta = opt.e.deltaY;
      let zoom = canvas.getZoom();
      zoom *= 0.999 ** delta;
      if (zoom > 20) zoom = 20;
      if (zoom < 0.01) zoom = 0.01;
      canvas.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);
      opt.e.preventDefault();
      opt.e.stopPropagation();
    });

    // Pan functionality
    canvas.on('mouse:down', function(opt) {
      const evt = opt.e;
      if (evt.altKey === true) {
        this.isDragging = true;
        this.selection = false;
        this.lastPosX = evt.clientX;
        this.lastPosY = evt.clientY;
      }
    });

    canvas.on('mouse:move', function(opt) {
      if (this.isDragging) {
        const e = opt.e;
        const vpt = this.viewportTransform;
        vpt[4] += e.clientX - this.lastPosX;
        vpt[5] += e.clientY - this.lastPosY;
        this.requestRenderAll();
        this.lastPosX = e.clientX;
        this.lastPosY = e.clientY;
      }
    });

    canvas.on('mouse:up', function(opt) {
      this.setViewportTransform(this.viewportTransform);
      this.isDragging = false;
      this.selection = true;
    });

    // Set up canvas event listeners for history
    canvas.on('object:modified', () => {
      saveCanvasState(canvas);
    });

    canvas.on('object:added', () => {
      saveCanvasState(canvas);
    });

    canvas.on('object:removed', () => {
      saveCanvasState(canvas);
    });

    // Delete key functionality
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete' || e.key === 'Backspace') {
        const activeObjects = canvas.getActiveObjects();
        if (activeObjects.length) {
          activeObjects.forEach(obj => canvas.remove(obj));
          canvas.discardActiveObject();
          canvas.renderAll();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    setFabricCanvas(canvas);

    // Load initial image if provided
    if (initialImage) {
      fabric.Image.fromURL(initialImage, (img) => {
        img.scaleToWidth(400);
        img.center();
        canvas.add(img);
        canvas.renderAll();
      });
    }

    // Save initial state
    saveCanvasState(canvas);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      canvas.dispose();
    };
  }, [isOpen, initialImage]);

  const saveCanvasState = (canvas: fabric.Canvas) => {
    const state = JSON.stringify(canvas.toJSON());
    setCanvasHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(state);
      return newHistory;
    });
    setHistoryIndex(prev => prev + 1);
  };

  const undo = () => {
    if (historyIndex > 0 && fabricCanvas) {
      const prevState = canvasHistory[historyIndex - 1];
      fabricCanvas.loadFromJSON(prevState, () => {
        fabricCanvas.renderAll();
        setHistoryIndex(prev => prev - 1);
      });
    }
  };

  const redo = () => {
    if (historyIndex < canvasHistory.length - 1 && fabricCanvas) {
      const nextState = canvasHistory[historyIndex + 1];
      fabricCanvas.loadFromJSON(nextState, () => {
        fabricCanvas.renderAll();
        setHistoryIndex(prev => prev + 1);
      });
    }
  };

  const handleSave = () => {
    if (fabricCanvas && onSave) {
      const dataURL = fabricCanvas.toDataURL({
        format: 'png',
        quality: 1,
        multiplier: 2
      });
      onSave(dataURL);
    }
  };

  const handleExport = () => {
    if (fabricCanvas) {
      const dataURL = fabricCanvas.toDataURL({
        format: 'png',
        quality: 1,
        multiplier: 2
      });

      // Create download link
      const link = document.createElement('a');
      link.download = 'canvas-design.png';
      link.href = dataURL;
      link.click();
    }
  };

  const handleZoomIn = () => {
    if (fabricCanvas) {
      const zoom = fabricCanvas.getZoom();
      fabricCanvas.setZoom(Math.min(zoom * 1.1, 20));
    }
  };

  const handleZoomOut = () => {
    if (fabricCanvas) {
      const zoom = fabricCanvas.getZoom();
      fabricCanvas.setZoom(Math.max(zoom * 0.9, 0.01));
    }
  };

  const handleResetZoom = () => {
    if (fabricCanvas) {
      fabricCanvas.setZoom(1);
      fabricCanvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
    }
  };

  if (!isOpen) return null;

  return (
    <div className={cn(
      "fixed inset-0 z-50 bg-neutral-900 flex flex-col",
      className
    )}>
      {/* Header/Toolbar */}
      <CanvasToolbar
        onClose={onClose}
        onSave={handleSave}
        onExport={handleExport}
        onUndo={undo}
        onRedo={redo}
        canUndo={historyIndex > 0}
        canRedo={historyIndex < canvasHistory.length - 1}
        selectedTool={selectedTool}
        onToolChange={setSelectedTool}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onResetZoom={handleResetZoom}
      />

      <div className="flex flex-1 min-h-0">
        {/* Left Sidebar */}
        <CanvasSidebar
          canvas={fabricCanvas}
          selectedTool={selectedTool}
          onToolChange={setSelectedTool}
        />

        {/* Main Canvas Area */}
        <div className="flex-1 flex flex-col items-center justify-center bg-neutral-800 p-4">
          <div className="bg-white rounded-lg shadow-2xl p-4">
            <canvas
              ref={canvasRef}
              className="border border-gray-200 rounded"
            />
          </div>
          <div className="mt-4 text-center text-gray-400 text-sm">
            <p>• Mouse wheel to zoom • Alt + drag to pan • Delete key to remove selected objects</p>
          </div>
        </div>
      </div>
    </div>
  );
};
