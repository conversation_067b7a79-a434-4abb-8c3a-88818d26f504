'use client'

import React from 'react';
import { Button } from '@/common/components/atoms';
import {
  X,
  Save,
  Download,
  Undo,
  Redo,
  MousePointer,
  Type,
  Square,
  Circle,
  Image as ImageIcon,
  ZoomIn,
  ZoomOut,
  RotateCcw
} from 'lucide-react';

interface CanvasToolbarProps {
  onClose: () => void;
  onSave?: () => void;
  onExport: () => void;
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  selectedTool: string;
  onToolChange: (tool: string) => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onResetZoom?: () => void;
}

export const CanvasToolbar = ({
  onClose,
  onSave,
  onExport,
  onUndo,
  onRedo,
  canUndo,
  canRedo,
  selectedTool,
  onToolChange,
  onZoomIn,
  onZoomOut,
  onResetZoom
}: CanvasToolbarProps) => {
  const tools = [
    { id: 'select', icon: MousePointer, label: 'Select' },
    { id: 'text', icon: Type, label: 'Text' },
    { id: 'rectangle', icon: Square, label: 'Rectangle' },
    { id: 'circle', icon: Circle, label: 'Circle' },
    { id: 'image', icon: ImageIcon, label: 'Image' },
  ];

  return (
    <div className="bg-neutral-800 border-b border-neutral-700 px-4 py-3 flex items-center justify-between">
      {/* Left side - Tools */}
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-1 mr-4">
          {tools.map((tool) => {
            const Icon = tool.icon;
            return (
              <button
                key={tool.id}
                onClick={() => onToolChange(tool.id)}
                className={`p-2 rounded-lg transition-colors ${
                  selectedTool === tool.id
                    ? 'bg-violets-are-blue text-white'
                    : 'text-gray-400 hover:text-white hover:bg-neutral-700'
                }`}
                title={tool.label}
              >
                <Icon size={18} />
              </button>
            );
          })}
        </div>

        {/* History Controls */}
        <div className="flex items-center gap-1">
          <button
            onClick={onUndo}
            disabled={!canUndo}
            className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Undo"
          >
            <Undo size={18} />
          </button>
          <button
            onClick={onRedo}
            disabled={!canRedo}
            className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Redo"
          >
            <Redo size={18} />
          </button>
        </div>

        {/* Zoom Controls */}
        <div className="flex items-center gap-1 ml-4">
          <button
            onClick={onZoomOut}
            className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700"
            title="Zoom Out"
          >
            <ZoomOut size={18} />
          </button>
          <button
            onClick={onResetZoom}
            className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700"
            title="Reset Zoom"
          >
            <RotateCcw size={18} />
          </button>
          <button
            onClick={onZoomIn}
            className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700"
            title="Zoom In"
          >
            <ZoomIn size={18} />
          </button>
        </div>
      </div>

      {/* Center - Title */}
      <div className="flex-1 text-center">
        <h1 className="text-white font-semibold text-lg">Canvas Editor</h1>
      </div>

      {/* Right side - Actions */}
      <div className="flex items-center gap-2">
        {onSave && (
          <Button
            variant="outline"
            size="sm"
            onClick={onSave}
            className="text-white border-neutral-600 hover:bg-neutral-700"
          >
            <Save size={16} className="mr-2" />
            Save
          </Button>
        )}
        
        <Button
          variant="outline"
          size="sm"
          onClick={onExport}
          className="text-white border-neutral-600 hover:bg-neutral-700"
        >
          <Download size={16} className="mr-2" />
          Export
        </Button>

        <button
          onClick={onClose}
          className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700"
          title="Close"
        >
          <X size={20} />
        </button>
      </div>
    </div>
  );
};
