'use client'

import React, { useState } from 'react';
import { fabric } from 'fabric';
import {
  Wand2,
  Upload,
  Type,
  Shapes,
  Clock,
  Image as ImageIcon,
  Square,
  Circle
} from 'lucide-react';
import { ImageStyles } from '@/common/constants';
import { getPath } from '@/common/utils/helpers';
import toast from 'react-hot-toast';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';

interface CanvasSidebarProps {
  canvas: fabric.Canvas | null;
  selectedTool: string;
  onToolChange: (tool: string) => void;
  agentId?: string;
  planId?: string;
}

interface SidebarTab {
  id: string;
  icon: React.ComponentType<{ size?: number }>;
  label: string;
  content: React.ComponentType<{
    canvas: fabric.Canvas | null;
    agentId?: string;
    planId?: string;
  }>;
}

// Placeholder components for each tab
const GenerateImagePanel = ({
  canvas,
  agentId,
  planId
}: {
  canvas: fabric.Canvas | null;
  agentId?: string;
  planId?: string;
}) => {
  const [imagePrompt, setImagePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState(ImageStyles[0]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');
  const { trackContentEvent } = useMixpanelEvent();

  const handleGenerate = async () => {
    if (!imagePrompt.trim()) {
      setError('Please enter an image description');
      return;
    }

    if (imagePrompt.length < 10) {
      setError('Description should be at least 10 characters');
      return;
    }

    if (imagePrompt.length > 400) {
      setError('Description should not exceed 400 characters');
      return;
    }

    if (!agentId) {
      setError('Agent ID is required for image generation');
      return;
    }

    setError('');
    setIsGenerating(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/${agentId}/post-image-gen`;

      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify({
          description: imagePrompt,
          style: selectedStyle.option,
          planId: planId || 'canvas-editor',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to generate image: ${response.statusText}`);
      }

      const imageData = await response.json();

      if (imageData && imageData.filepath && canvas) {
        const imageUrl = getPath(imageData.filepath);

        // Add the generated image to the canvas
        fabric.Image.fromURL(imageUrl, (img) => {
          img.scaleToWidth(300);
          img.center();
          canvas.add(img);
          canvas.renderAll();
        });

        trackContentEvent('canvas_image_generation', {
          prompt: imagePrompt,
          imageStyle: selectedStyle.option,
        });

        toast.success('Image generated and added to canvas!');
        setImagePrompt(''); // Clear the prompt after successful generation
      } else {
        throw new Error('Invalid response from image generation API');
      }
    } catch (error) {
      console.error('Error generating image:', error);
      toast.error('Failed to generate image');
      setError('Failed to generate image. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h3 className="text-white font-semibold text-lg mb-2">Generate Image</h3>
        <p className="text-gray-400 text-sm">Create images using AI</p>
      </div>
      <div className="space-y-4">
        <div>
          <label className="text-white text-sm font-medium mb-2 block">Description</label>
          <textarea
            value={imagePrompt}
            onChange={(e) => setImagePrompt(e.target.value)}
            placeholder="Describe the image you want to generate..."
            className="w-full h-24 p-3 bg-neutral-800 text-white rounded-lg border border-neutral-600 resize-none focus:border-violets-are-blue focus:outline-none transition-colors"
            disabled={isGenerating}
          />
          <div className="text-xs text-gray-400 mt-1">
            {imagePrompt.length}/400 characters
          </div>
        </div>

        <div>
          <label className="text-white text-sm font-medium mb-2 block">Style</label>
          <select
            value={selectedStyle.option}
            onChange={(e) => {
              const style = ImageStyles.find(s => s.option === e.target.value);
              if (style) setSelectedStyle(style);
            }}
            className="w-full bg-neutral-800 text-white border border-neutral-600 rounded-lg p-2 focus:border-violets-are-blue focus:outline-none"
            disabled={isGenerating}
          >
            {ImageStyles.map((style) => (
              <option key={style.option} value={style.option}>
                {style.label}
              </option>
            ))}
          </select>
        </div>

        {error && (
          <div className="text-red-400 text-sm bg-red-400/10 p-2 rounded-lg">
            {error}
          </div>
        )}

        <button
          onClick={handleGenerate}
          disabled={isGenerating || !imagePrompt.trim()}
          className="w-full bg-gradient-to-r from-violets-are-blue to-han-purple hover:from-violets-are-blue/90 hover:to-han-purple/90 text-white py-3 px-4 rounded-lg transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isGenerating ? 'Generating...' : 'Generate Image'}
        </button>
      </div>
    </div>
  );
};

const UploadImagePanel = ({ canvas }: { canvas: fabric.Canvas | null; agentId?: string; planId?: string; }) => (
  <div className="p-6">
    <div className="mb-6">
      <h3 className="text-white font-semibold text-lg mb-2">Upload Image</h3>
      <p className="text-gray-400 text-sm">Add images from your device</p>
    </div>
    <div className="space-y-4">
      <div className="border-2 border-dashed border-neutral-600 hover:border-violets-are-blue rounded-lg p-8 text-center transition-colors cursor-pointer">
        <ImageIcon className="mx-auto mb-3 text-gray-400" size={40} />
        <p className="text-gray-400 text-sm mb-2">Drag & drop or click to upload</p>
        <p className="text-gray-500 text-xs">PNG, JPG, GIF up to 10MB</p>
      </div>
      <input
        type="file"
        accept="image/*"
        className="w-full text-white bg-neutral-800 border border-neutral-600 rounded-lg p-2 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-violets-are-blue file:text-white hover:file:bg-violets-are-blue/80"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file && canvas) {
            const reader = new FileReader();
            reader.onload = (event) => {
              const imgUrl = event.target?.result as string;
              fabric.Image.fromURL(imgUrl, (img) => {
                img.scaleToWidth(200);
                img.center();
                canvas.add(img);
                canvas.renderAll();
              });
            };
            reader.readAsDataURL(file);
          }
        }}
      />
    </div>
  </div>
);

const TextPanel = ({ canvas }: { canvas: fabric.Canvas | null; agentId?: string; planId?: string; }) => (
  <div className="p-6">
    <div className="mb-6">
      <h3 className="text-white font-semibold text-lg mb-2">Text Tools</h3>
      <p className="text-gray-400 text-sm">Add and customize text</p>
    </div>
    <div className="space-y-6">
      <button
        onClick={() => {
          if (canvas) {
            const text = new fabric.Text('Click to edit', {
              left: 100,
              top: 100,
              fontFamily: 'Arial',
              fontSize: 24,
              fill: '#000000'
            });
            canvas.add(text);
            canvas.renderAll();
          }
        }}
        className="w-full bg-gradient-to-r from-violets-are-blue to-han-purple hover:from-violets-are-blue/90 hover:to-han-purple/90 text-white py-3 px-4 rounded-lg transition-all duration-200 font-medium shadow-lg"
      >
        Add Text
      </button>

      <div className="space-y-4">
        <div>
          <label className="text-white text-sm font-medium mb-2 block">Font Family</label>
          <select className="w-full bg-neutral-800 text-white border border-neutral-600 rounded-lg p-2 focus:border-violets-are-blue focus:outline-none">
            <option>Arial</option>
            <option>Helvetica</option>
            <option>Times New Roman</option>
            <option>Georgia</option>
            <option>Verdana</option>
          </select>
        </div>

        <div>
          <label className="text-white text-sm font-medium mb-2 block">Font Size</label>
          <input
            type="range"
            min="12"
            max="72"
            defaultValue="24"
            className="w-full accent-violets-are-blue"
          />
        </div>

        <div>
          <label className="text-white text-sm font-medium mb-2 block">Color</label>
          <input
            type="color"
            defaultValue="#000000"
            className="w-full h-10 rounded-lg border border-neutral-600"
          />
        </div>
      </div>
    </div>
  </div>
);

const ElementsPanel = ({ canvas }: { canvas: fabric.Canvas | null; agentId?: string; planId?: string; }) => (
  <div className="p-6">
    <div className="mb-6">
      <h3 className="text-white font-semibold text-lg mb-2">Elements</h3>
      <p className="text-gray-400 text-sm">Add shapes and elements</p>
    </div>
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-3">
        <button
          onClick={() => {
            if (canvas) {
              const rect = new fabric.Rect({
                left: 100,
                top: 100,
                width: 100,
                height: 100,
                fill: '#7E5EF2',
                stroke: '#000000',
                strokeWidth: 1
              });
              canvas.add(rect);
              canvas.renderAll();
            }
          }}
          className="bg-neutral-800 hover:bg-neutral-700 text-white py-4 px-4 rounded-lg transition-colors border border-neutral-600 hover:border-violets-are-blue flex flex-col items-center gap-2"
        >
          <Square size={24} />
          <span className="text-xs">Rectangle</span>
        </button>
        <button
          onClick={() => {
            if (canvas) {
              const circle = new fabric.Circle({
                left: 100,
                top: 100,
                radius: 50,
                fill: '#FE8989',
                stroke: '#000000',
                strokeWidth: 1
              });
              canvas.add(circle);
              canvas.renderAll();
            }
          }}
          className="bg-neutral-800 hover:bg-neutral-700 text-white py-4 px-4 rounded-lg transition-colors border border-neutral-600 hover:border-violets-are-blue flex flex-col items-center gap-2"
        >
          <Circle size={24} />
          <span className="text-xs">Circle</span>
        </button>
      </div>

      <div className="space-y-3">
        <div>
          <label className="text-white text-sm font-medium mb-2 block">Fill Color</label>
          <input
            type="color"
            defaultValue="#7E5EF2"
            className="w-full h-10 rounded-lg border border-neutral-600"
          />
        </div>

        <div>
          <label className="text-white text-sm font-medium mb-2 block">Stroke Color</label>
          <input
            type="color"
            defaultValue="#000000"
            className="w-full h-10 rounded-lg border border-neutral-600"
          />
        </div>
      </div>
    </div>
  </div>
);

const RecentUploadsPanel = ({ canvas }: { canvas: fabric.Canvas | null; agentId?: string; planId?: string; }) => (
  <div className="p-6">
    <div className="mb-6">
      <h3 className="text-white font-semibold text-lg mb-2">Recent Uploads</h3>
      <p className="text-gray-400 text-sm">Your recently uploaded images</p>
    </div>
    <div className="text-gray-400 text-sm text-center py-12">
      <ImageIcon className="mx-auto mb-3 text-gray-500" size={32} />
      <p>No recent uploads</p>
      <p className="text-xs mt-1">Upload images to see them here</p>
    </div>
  </div>
);

export const CanvasSidebar = ({ canvas, selectedTool, onToolChange, agentId, planId }: CanvasSidebarProps) => {
  const [activeTab, setActiveTab] = useState('generate');

  const tabs: SidebarTab[] = [
    { id: 'generate', icon: Wand2, label: 'Generate', content: GenerateImagePanel },
    { id: 'upload', icon: Upload, label: 'Upload', content: UploadImagePanel },
    { id: 'text', icon: Type, label: 'Text', content: TextPanel },
    { id: 'elements', icon: Shapes, label: 'Elements', content: ElementsPanel },
    { id: 'recent', icon: Clock, label: 'Recent', content: RecentUploadsPanel },
  ];

  const ActiveContent = tabs.find(tab => tab.id === activeTab)?.content || GenerateImagePanel;

  return (
    <div className="w-80 bg-neutral-900 border-r border-neutral-700 flex h-full">
      {/* Tab Navigation */}
      <div className="w-16 bg-neutral-800 flex flex-col border-r border-neutral-700">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`p-3 flex flex-col items-center gap-1 transition-all duration-200 border-r-2 ${
                activeTab === tab.id
                  ? 'bg-violets-are-blue text-white border-violets-are-blue shadow-lg'
                  : 'text-gray-400 hover:text-white hover:bg-neutral-700 border-transparent'
              }`}
              title={tab.label}
            >
              <Icon size={18} />
              <span className="text-xs font-medium">{tab.label}</span>
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto bg-neutral-900">
        <div className="h-full">
          <ActiveContent canvas={canvas} />
        </div>
      </div>
    </div>
  );
};
