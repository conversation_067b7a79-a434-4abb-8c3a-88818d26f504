import {
  isProd, mixpanelToken,
} from '@/common/constants';
import mixpanel, { Dict } from 'mixpanel-browser';

if (mixpanelToken) {
  if (isProd) {
    mixpanel.init(mixpanelToken, {
      record_sessions_percent: 1,
    });
    mixpanel.start_session_recording()
  } else {
    mixpanel.init(mixpanelToken);
  }

}
export const actions = {
  identify: (id: string) => {
    mixpanel.identify(id);
  },
  alias: (id: string) => {
    mixpanel.alias(id);
  },
  track: (name: string, props: Dict = {}) => {
    mixpanel.track(name, props);
  },
  people: {
    set: (props: Dict) => {
      mixpanel.people.set(props);
    },
  },
  register: (props: Dict) => {
    mixpanel.register(props);
  },
  getDistinctId: () => {
    return mixpanel.get_distinct_id();
  },
  reset: () => {
    mixpanel.reset();
  },
};

let hasAuthenticatedUser = false;
let lastAuthenticatedUserId: string | null = null;

export const handleUserAuthentication = (userId: string, userProps: Dict = {}) => {
  if (hasAuthenticatedUser && lastAuthenticatedUserId === userId) {
    return;
  }

  const anonymousId = actions.getDistinctId();

  if (typeof window !== 'undefined') {
    localStorage.setItem('mp_anonymous_id', anonymousId);
    localStorage.setItem('mp_last_auth_user', userId);
  }

  actions.alias(userId);
  actions.identify(userId);

  actions.people.set({
    ...userProps,
    $first_login: new Date().toISOString(),
    anonymous_id: anonymousId, // Store the anonymous ID as a property
  });

  actions.track('User Authenticated', {
    anonymous_id: anonymousId,
    user_id: userId,
    timestamp: Date.now(),
  });

  hasAuthenticatedUser = true;
  lastAuthenticatedUserId = userId;
};

export const resetAuthenticationState = () => {
  hasAuthenticatedUser = false;
  lastAuthenticatedUserId = null;
  if (typeof window !== 'undefined') {
    localStorage.removeItem('mp_last_auth_user');
  }
};
