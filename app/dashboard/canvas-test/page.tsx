'use client'

import React, { useState } from 'react';
import { CanvasEditor } from '@/common/components/organisms';
import { Button } from '@/common/components/atoms';

export default function CanvasTestPage() {
  const [isCanvasOpen, setIsCanvasOpen] = useState(false);

  const handleSave = (imageUrl: string) => {
    console.log('Canvas saved:', imageUrl);
    // Here you would typically save the image to your backend
    setIsCanvasOpen(false);
  };

  return (
    <div className="min-h-screen bg-neutral-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-white text-3xl font-bold mb-8">Canvas Editor Test</h1>
        
        <div className="space-y-4">
          <p className="text-gray-300">
            Click the button below to open the Canvas Editor and test the Canva-like interface.
          </p>
          
          <Button
            variant="gradient"
            size="lg"
            onClick={() => setIsCanvasOpen(true)}
          >
            Open Canvas Editor
          </Button>
        </div>

        <CanvasEditor
          isOpen={isCanvasOpen}
          onClose={() => setIsCanvasOpen(false)}
          onSave={handleSave}
        />
      </div>
    </div>
  );
}
